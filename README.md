# Sanskar Institute Attendance Management System

A comprehensive web-based attendance management system built with PHP and MySQL, designed for Sanskar Institute of Management and Information Technology.

## Features

### 🎯 Multi-Role System
- **Admin Dashboard**: Complete system management
- **Teacher Portal**: Attendance marking and class management
- **Student Portal**: View attendance records and reports

### 📊 Core Functionality
- **Attendance Management**: Create sessions, mark attendance, view records
- **User Management**: Manage students, teachers, and administrators
- **Department & Course Management**: Organize academic structure
- **Class Management**: Assign teachers and enroll students
- **Reporting**: Generate detailed attendance reports
- **Real-time Statistics**: Dashboard with key metrics

### 🎨 Modern UI/UX
- **Responsive Design**: Works on desktop, tablet, and mobile
- **Tailwind CSS**: Modern, clean interface
- **Font Awesome Icons**: Professional iconography
- **Interactive Elements**: Smooth transitions and hover effects

## Technology Stack

- **Backend**: PHP 7.4+
- **Database**: MySQL 5.7+
- **Frontend**: HTML5, CSS3, JavaScript
- **CSS Framework**: Tailwind CSS (CDN)
- **Icons**: Font Awesome 6
- **Server**: Apache/Nginx with PHP support

## Installation & Setup

### Prerequisites
- XAMPP/WAMP/LAMP stack
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web browser (Chrome, Firefox, Safari, Edge)

### Step 1: Download & Extract
1. Download the project files
2. Extract to your web server directory:
   - **XAMPP**: `C:\xampp\htdocs\attendance-module`
   - **WAMP**: `C:\wamp64\www\attendance-module`
   - **Linux**: `/var/www/html/attendance-module`

### Step 2: Database Setup
1. Start your MySQL server
2. Open phpMyAdmin or MySQL command line
3. Import the database schema:
   ```sql
   mysql -u root -p < config/schema.sql
   ```
   Or manually run the SQL commands from `config/schema.sql`

### Step 3: Database Configuration
1. Open `config/database.php`
2. Update database credentials if needed:
   ```php
   private $host = 'localhost';
   private $db_name = 'attendance_system';
   private $username = 'root';
   private $password = '';
   ```

### Step 4: Access the System
1. Start your web server (Apache)
2. Open your browser and navigate to:
   ```
   http://localhost/attendance-module
   ```

## Default Login Credentials

### Administrator
- **Username**: `admin`
- **Password**: `password`
- **Email**: `<EMAIL>`

### Demo Accounts
The system includes demo credentials for testing:
- **Admin**: admin / password
- **Teacher**: teacher / password  
- **Student**: student / password

*Note: You'll need to create teacher and student accounts through the admin panel or add them manually to the database.*

## Project Structure

```
attendance-module/
├── admin/                  # Admin dashboard and management
│   ├── dashboard.php
│   ├── users.php
│   ├── departments.php
│   └── ...
├── teacher/               # Teacher portal
│   ├── dashboard.php
│   ├── mark-attendance.php
│   ├── view-attendance.php
│   └── ...
├── student/               # Student portal
│   ├── dashboard.php
│   ├── my-attendance.php
│   └── ...
├── auth/                  # Authentication
│   ├── login.php
│   └── logout.php
├── config/                # Configuration files
│   ├── database.php
│   └── schema.sql
├── includes/              # Shared components
│   ├── header.php
│   └── footer.php
├── index.php             # Main entry point
└── README.md
```

## Database Schema

### Core Tables
- **users**: System users (admin, teachers, students)
- **departments**: Academic departments
- **courses**: Course catalog
- **classes**: Class sections with teacher assignments
- **enrollments**: Student-class relationships
- **attendance_sessions**: Individual attendance sessions
- **attendance_records**: Student attendance records

### Key Relationships
- Users can have roles: admin, teacher, student
- Teachers are assigned to classes
- Students are enrolled in classes
- Attendance sessions belong to classes
- Attendance records link students to sessions

## Usage Guide

### For Administrators
1. **Login** with admin credentials
2. **Manage Users**: Add/edit teachers and students
3. **Setup Departments**: Create academic departments
4. **Add Courses**: Define course catalog
5. **Create Classes**: Assign teachers to course sections
6. **Enroll Students**: Add students to classes
7. **View Reports**: Monitor system-wide attendance

### For Teachers
1. **Login** with teacher credentials
2. **View Dashboard**: See assigned classes and statistics
3. **Mark Attendance**: Create sessions and mark student attendance
4. **View Records**: Check attendance history
5. **Generate Reports**: Create class-specific reports

### For Students
1. **Login** with student credentials
2. **View Dashboard**: See enrollment and attendance overview
3. **Check Attendance**: View personal attendance records
4. **Class Information**: See enrolled courses and teachers
5. **Generate Reports**: Export personal attendance data

## Customization

### Styling
- Modify Tailwind classes in PHP files for UI changes
- Custom CSS can be added to `includes/header.php`
- Color scheme can be adjusted using Tailwind configuration

### Features
- Add new user roles by modifying the database schema
- Extend reporting functionality in respective role directories
- Implement additional attendance statuses (e.g., excused, medical)

### Branding
- Update institute name in `auth/login.php` and `includes/header.php`
- Modify logos and colors to match institutional branding
- Customize email templates and notifications

## Security Features

- **Session Management**: Secure PHP sessions
- **Password Hashing**: bcrypt password encryption
- **SQL Injection Protection**: Prepared statements
- **Role-based Access**: Route protection by user role
- **Input Validation**: Server-side form validation
- **XSS Protection**: HTML escaping for output

## Browser Support

- Chrome 70+
- Firefox 65+
- Safari 12+
- Edge 79+
- Mobile browsers (iOS Safari, Chrome Mobile)

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check MySQL service is running
   - Verify database credentials in `config/database.php`
   - Ensure database exists and schema is imported

2. **Login Issues**
   - Verify default admin account exists in database
   - Check password hashing (use PHP's `password_hash()`)
   - Clear browser cache and cookies

3. **Permission Errors**
   - Ensure web server has read/write permissions
   - Check file ownership and permissions
   - Verify PHP error reporting is enabled

4. **Styling Issues**
   - Check internet connection for CDN resources
   - Verify Tailwind CSS and Font Awesome CDN links
   - Clear browser cache

### Error Logs
- Check PHP error logs in your server configuration
- Enable error reporting in development:
  ```php
  error_reporting(E_ALL);
  ini_set('display_errors', 1);
  ```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is developed for educational purposes for Sanskar Institute of Management and Information Technology.

## Support

For technical support or questions:
- Check the troubleshooting section
- Review the code documentation
- Contact the development team

---

**Sanskar Institute of Management & Information Technology**  
*Student Attendance Management System*  
Version 1.0 - 2024
